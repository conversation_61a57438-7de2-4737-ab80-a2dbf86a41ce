<?php
/*
 * Include the application_top.php script
 */
include_once('includes/application_top.php');

/*
 * Send the XML content header
 */
header('Content-Type: application/xml; charset=utf-8');

/*
 * Echo the XML declaration
 */
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
?>

<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
<?php
/*
 * Helper to create one <url> node
 */
function GenerateNode($data) {
    $content  = "\t<url>\n";
    $content .= "\t\t<loc>" . htmlspecialchars($data['loc'], ENT_XML1, 'UTF-8') . "</loc>\n";
    $content .= "\t\t<lastmod>" . htmlspecialchars($data['lastmod'], ENT_XML1, 'UTF-8') . "</lastmod>\n";
    $content .= "\t\t<changefreq>" . htmlspecialchars($data['changefreq'], ENT_XML1, 'UTF-8') . "</changefreq>\n";
    $content .= "\t\t<priority>" . htmlspecialchars($data['priority'], ENT_XML1, 'UTF-8') . "</priority>\n";
    $content .= "\t</url>\n";
    return $content;
}

/*
 * Categories query (note the explicit JOIN for cd)
 */
$sql = "
    SELECT 
        c.categories_id   AS cID,
        c.date_added      AS category_date_added,
        c.last_modified   AS category_last_mod,
        cd.categories_url_override AS url_override
    FROM " . TABLE_CATEGORIES . " c
    JOIN " . TABLE_CATEGORIES_TO_STORES . " c2s
         ON c.categories_id = c2s.categories_id
    JOIN " . TABLE_CATEGORIES_DESCRIPTION . " cd
         ON c.categories_id = cd.categories_id
    WHERE c.categories_status IN (1,2,3,4)
      AND c2s.store_cg = '1'
      AND c.categories_id != '2970'
    ORDER BY c.parent_id ASC, c.sort_order ASC, c.categories_id ASC
";

$query = tep_db_query($sql);

if (tep_db_num_rows($query) > 0) {
    /*
     * Collect categories with lastmod and override URL
     */
    $container = array();
    while ($row = tep_db_fetch_array($query)) {
        $lastmod = max($row['category_date_added'], $row['category_last_mod']);
        $container[$row['cID']] = array(
            'lastmod'     => $lastmod,
            'url_override'=> $row['url_override']
        );
    }
    tep_db_free_result($query);

    // Sort by lastmod descending
    uasort($container, function ($a, $b) {
        return strcmp($b['lastmod'], $a['lastmod']);
    });

    $total  = count($container);
    $_total = $total;

    foreach ($container as $cID => $data) {
        // Use override if present, otherwise build standard link
        if (tep_not_null($data['url_override'])) {
            $location = $data['url_override'];
        } else {
            $location = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $cID, 'NONSSL', false);
        }

        $node_data = array(
            'loc'        => $location,
            'lastmod'    => date('Y-m-d', strtotime($data['lastmod'])),
            'changefreq' => 'weekly',
            'priority'   => max(number_format($_total / $total, 1, '.', ','), 0.1)
        );
        $_total--;

        echo GenerateNode($node_data);
    }
}
echo '</urlset>';
?>
